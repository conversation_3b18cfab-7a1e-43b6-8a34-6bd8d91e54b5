package com.yyigou.ddc.dmp.web.vo.dataset;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class DatasetPreviewVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 数据集唯一标识
     */
    private String datasetNo;

    /**
     * 预览数据列表
     */
    private List<Map<String, Object>> data;

    /**
     * 字段信息列表
     */
    private List<ColumnInfo> columns;

    /**
     * 总记录数
     */
    private Integer totalCount;

    @Data
    public static class ColumnInfo implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 字段名称
         */
        private String fieldName;

        /**
         * 字段显示名称
         */
        private String displayName;

        /**
         * 字段数据类型
         */
        private String dataType;

        /**
         * 字段来源表
         */
        private String sourceTable;

        /**
         * 字段来源schema
         */
        private String sourceSchema;
    }
}
