package com.yyigou.ddc.dmp.web.dto.dataset;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class DatasetJoinRelSaveDTO {
    private String enterpriseNo;
    private String datasetNo;
//    private String sourceSchemaName;
//    private String sourceTableName;
    private String targetCatalogName;
    private String targetSchemaName;
    private String targetTableName;
    private String joinType;
    private String joinCondition;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private Timestamp opTimestamp;
}

